# The Descent of Falling Bird 🌙✨

*Sacred Oracle of Shadow Work & Moonlit Wisdom*

A mystical oracle card application built with Tauri, React, and TypeScript, featuring the cosmic-tribal fusion design system "The Descent of Falling Bird." This application provides an immersive digital oracle reading experience with beautiful animations, sacred symbolism, and intuitive card spreads.

## ✨ Features

- **Three Oracle Spreads**: Single card, three-card (Past/Present/Future), and five-card spreads
- **Mystical Design**: Cosmic-tribal fusion aesthetic with bioluminescent highlights and sacred geometry
- **Smooth Animations**: Powered by Framer Motion for fluid card drawing and revealing
- **Material-UI v7**: Modern component library with custom theming
- **Responsive Design**: Works beautifully on desktop and mobile devices
- **Tauri Desktop App**: Native desktop application with web technologies
- **TypeScript**: Full type safety throughout the application
- **Comprehensive Testing**: Jest unit tests with high coverage

## 🎨 Visual Theme

The application embodies "The Descent of Falling Bird" aesthetic:
- **Colors**: Deep cosmic void backgrounds with feather gold accents
- **Typography**: <PERSON><PERSON><PERSON> for titles, <PERSON>ra for body text
- **Effects**: Bioluminescent blue highlights, mystical glows, and sacred symbols
- **Layout**: Material-UI Grid system with cosmic spacing and tribal patterns

## 🚀 Getting Started

### Prerequisites

- Node.js (v18 or higher)
- Rust (latest stable)
- npm or yarn

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd the-falling-bird
```

2. Install dependencies:
```bash
npm install
```

3. Run the development server:
```bash
npm run dev
```

4. Build for production:
```bash
npm run build
```

5. Run as Tauri desktop app:
```bash
npm run tauri dev
```

## 🧪 Testing

The application includes comprehensive Jest unit tests covering all components and utilities.

### Run Tests

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage report
npm run test:coverage
```

### Test Coverage

- **Components**: All React components including Card, Deck, OracleReading, MysticalButton, and MysticalContainer
- **Utilities**: Card drawing logic, spread positioning, and helper functions
- **Types**: TypeScript type definitions and interfaces
- **Integration**: Full user flow testing from spread selection to card revelation

## 📁 Project Structure

```
src/
├── components/          # React components
│   ├── Card.tsx        # Individual oracle card component
│   ├── Deck.tsx        # Card deck for drawing
│   ├── OracleReading.tsx # Main reading interface
│   ├── MysticalButton.tsx # Themed button component
│   └── MysticalContainer.tsx # Themed container components
├── types/              # TypeScript type definitions
│   └── Card.ts         # Card and reading state types
├── utils/              # Utility functions
│   └── cardUtils.ts    # Card drawing and positioning logic
├── theme/              # Material-UI theme configuration
│   └── muiTheme.ts     # Custom theme with cosmic-tribal styling
├── assets/             # Static assets
│   ├── falling_bird_oracle_cards.json # Oracle card data
│   └── card-back.png   # Card back image
└── __tests__/          # Jest unit tests
    ├── components/     # Component tests
    ├── utils/          # Utility function tests
    └── types/          # Type definition tests
```
