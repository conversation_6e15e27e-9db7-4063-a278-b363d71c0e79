{"name": "the-falling-bird", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "tauri": "tauri", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^7.1.1", "@mui/material": "^7.1.1", "@mui/styled-engine-sc": "^7.1.1", "@tauri-apps/api": "^2", "@tauri-apps/plugin-opener": "^2", "framer-motion": "^12.16.0", "react": "^18.3.1", "react-dom": "^18.3.1"}, "devDependencies": {"@tauri-apps/cli": "^2", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^29.5.14", "@types/react": "^18.3.1", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.3.4", "identity-obj-proxy": "^3.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^30.0.0-beta.3", "ts-jest": "^29.3.4", "typescript": "~5.6.2", "vite": "^6.0.3"}}