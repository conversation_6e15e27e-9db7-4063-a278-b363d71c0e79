export interface OracleCard {
  title: string;
  suite: string;
  poetic_meaning: string;
  affirmation: string;
  journal_prompt: string;
  reversal: string;
  reversal_meaning: string;
  key_interpretation: string;
}

export interface DrawnCard extends OracleCard {
  id: string;
  isRevealed: boolean;
  isReversed: boolean;
  position: number;
}

export type SpreadType = 'single' | 'three';

export interface ReadingState {
  phase: 'question' | 'shuffle' | 'selection' | 'drawing' | 'drawing-cards' | 'reading' | 'complete';
  spreadType: SpreadType | null;
  drawnCards: DrawnCard[];
  isShuffling: boolean;
  deckShuffled: boolean;
}
