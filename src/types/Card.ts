export interface OracleCard {
  title: string;
  suite: string;
  upright_meaning: string;
  upright_key_interpretation: string;
  upright_affirmation: string;
  upright_journal_prompt: string;
  reversal_key_interpretation: string;
  reversal_journal_prompt: string;
  reversal_meaning: string;
  reversal_affirmation: string;
}

export interface DrawnCard extends OracleCard {
  id: string;
  isRevealed: boolean;
  isReversed: boolean;
  position: number;
}

export type SpreadType = 'single' | 'three';

export interface ReadingState {
  phase: 'question' | 'shuffle' | 'selection' | 'drawing' | 'drawing-cards' | 'reading' | 'complete';
  spreadType: SpreadType | null;
  drawnCards: DrawnCard[];
  isShuffling: boolean;
  deckShuffled: boolean;
}
