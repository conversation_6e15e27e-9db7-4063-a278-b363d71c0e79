import React from 'react';
import { motion } from 'framer-motion';
import { Box } from '@mui/material';
import cardBackImage from '../assets/card-back.png';

interface DeckProps {
  cardCount: number;
  isShuffling: boolean;
}

export const Deck: React.FC<DeckProps> = ({
  cardCount,
  isShuffling
}) => {
  const renderDeckCards = () => {
    const cards = [];
    const maxVisible = Math.min(cardCount, 8); // Show max 8 cards for visual effect

    for (let i = 0; i < maxVisible; i++) {
      cards.push(
        <Box
          key={i}
          component={motion.div}
          className="deck-card"
          sx={{
            position: 'absolute',
            width: { xs: 120, md: 140 },
            height: { xs: 180, md: 210 },
            borderRadius: '15px',
            overflow: 'hidden',
            boxShadow: '0 6px 20px rgba(10, 10, 15, 0.6), 0 0 0 2px #d4af37',
            border: '1px solid rgba(212, 175, 55, 0.3)',
            zIndex: maxVisible - i,
            transform: `translate(${i * 2}px, ${i * -1}px)`,
          }}
          animate={isShuffling ? {
            x: [0, Math.random() * 20 - 10, 0],
            y: [0, Math.random() * 20 - 10, 0],
            rotate: [0, Math.random() * 10 - 5, 0],
          } : {}}
          transition={{
            duration: 0.5,
            delay: i * 0.1,
            repeat: isShuffling ? Infinity : 0,
            repeatType: "reverse"
          }}
        >
          <Box
            component="img"
            src={cardBackImage}
            alt="Card back"
            sx={{
              width: '100%',
              height: '100%',
              objectFit: 'cover',
              filter: 'sepia(20%) saturate(1.2) brightness(0.9)',
            }}
          />
        </Box>
      );
    }
    return cards;
  };

  return (
    <Box
      className="deck-container"
      data-testid="deck-container"
      sx={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        width: '100%',
        height: '100%',
        position: 'relative',
      }}
    >
      <Box
        component={motion.div}
        className="deck"
        sx={{
          position: 'relative',
          display: 'inline-block',
          cursor: 'pointer',
          filter: 'drop-shadow(0 8px 20px rgba(212, 175, 55, 0.3))',
        }}
        whileHover={{ scale: 1.05 }}
      >
        {renderDeckCards()}
      </Box>
    </Box>
  );
};
