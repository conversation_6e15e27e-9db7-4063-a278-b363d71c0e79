import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Typography, Box, Stack, Grid } from '@mui/material';
import { OracleCard, ReadingState, SpreadType } from '../types/Card';
import { drawCards, getCardCount, getSpreadPositions, sleep } from '../utils/cardUtils';
import { Deck } from './Deck';
import { Card } from './Card';

import { MysticalButton } from './MysticalButton';
import { MysticalContainer, MysticalPaper } from './MysticalContainer';
import oracleCardsData from '../assets/falling_bird_oracle_cards.json';

export const OracleReading: React.FC = () => {
  const [cards] = useState<OracleCard[]>(oracleCardsData);
  const [readingState, setReadingState] = useState<ReadingState>({
    phase: 'question',
    spreadType: null,
    drawnCards: [],
    isShuffling: false,
    deckShuffled: false
  });

  const handleShuffle = async () => {
    setReadingState(prev => ({ ...prev, isShuffling: true }));
    await sleep(2000); // Shuffle animation duration
    setReadingState(prev => ({ 
      ...prev, 
      isShuffling: false, 
      deckShuffled: true,
      phase: 'selection'
    }));
  };

  const handleSpreadSelection = (spreadType: SpreadType) => {
    setReadingState(prev => ({ ...prev, spreadType, phase: 'drawing' }));

    // Draw cards after a brief delay
    setTimeout(() => {
      const cardCount = getCardCount(spreadType);
      const drawnCards = drawCards(cards, cardCount);
      setReadingState(prev => ({
        ...prev,
        drawnCards,
        phase: 'drawing-cards'
      }));

      // After drawing animation, switch to reading phase
      setTimeout(() => {
        setReadingState(prev => ({ ...prev, phase: 'reading' }));
      }, 1500); // Allow time for drawing animation
    }, 500);
  };

  const handleCardReveal = (cardId: string) => {
    setReadingState(prev => ({
      ...prev,
      drawnCards: prev.drawnCards.map(card =>
        card.id === cardId ? { ...card, isRevealed: true } : card
      )
    }));

    // Check if all cards are revealed
    const allRevealed = readingState.drawnCards.every(card => 
      card.id === cardId || card.isRevealed
    );
    
    if (allRevealed) {
      setTimeout(() => {
        setReadingState(prev => ({ ...prev, phase: 'complete' }));
      }, 1000);
    }
  };

  const resetReading = () => {
    setReadingState({
      phase: 'question',
      spreadType: null,
      drawnCards: [],
      isShuffling: false,
      deckShuffled: false
    });
  };

  const renderPhase = () => {
    switch (readingState.phase) {
      case 'question':
        return (
          <MysticalContainer
            className="question-phase"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            maxWidth="md"
          >
            <Typography variant="h2" component="h2" align="center" sx={{ mb: 3, color: 'primary.main' }}>
              Ask Your Question
            </Typography>
            <Typography variant="body1" align="center" sx={{ mb: 4, color: 'text.secondary', fontStyle: 'italic' }}>
              Take a moment to center yourself and focus on your question.
              What guidance do you seek from the oracle?
            </Typography>
            <Box className="question-area" sx={{ textAlign: 'center' }}>
              <Typography variant="body1" sx={{ mb: 3, color: 'text.secondary', fontStyle: 'italic' }}>
                Hold your question in your mind and heart...
              </Typography>
              <MysticalButton
                onClick={() => setReadingState(prev => ({ ...prev, phase: 'shuffle' }))}
                size="large"
              >
                I'm Ready
              </MysticalButton>
            </Box>
          </MysticalContainer>
        );

      case 'shuffle':
        return (
          <Box
            className="shuffle-phase"
            component={motion.div}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            sx={{
              width: '100%',
              minHeight: '60vh',
              display: 'flex',
              flexDirection: 'column',
              position: 'relative',
              p: 0,
              m: 0,
            }}
          >
            {/* Centered Deck Container */}
            <Box
              sx={{
                flex: 1,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                width: '100%',
                minHeight: 0, // Allow flex shrinking
              }}
            >
              <Deck
                cardCount={cards.length}
                isShuffling={readingState.isShuffling}
              />
            </Box>

            {/* Bottom Button Container */}
            <Box
              sx={{
                width: '100%',
                display: 'flex',
                justifyContent: 'center',
                pb: { xs: 4, md: 6 },
                pt: 2,
              }}
            >
              <MysticalButton
                onClick={handleShuffle}
                disabled={readingState.isShuffling}
                size="large"
              >
                {readingState.isShuffling ? 'Shuffling...' : 'Shuffle Deck'}
              </MysticalButton>
            </Box>
          </Box>
        );

      case 'selection':
        return (
          <MysticalContainer
            className="selection-phase"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            maxWidth="lg"
          >
            <Typography variant="h2" component="h2" align="center" sx={{ mb: 4, color: 'primary.main' }}>
              Choose Your Spread
            </Typography>
            <Stack
              direction={{ xs: 'column', md: 'row' }}
              spacing={4}
              justifyContent="center"
              alignItems="center"
              sx={{ mt: 2 }}
            >
              <MysticalPaper
                className="spread-option"
                onClick={() => handleSpreadSelection('single')}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                showDecorator={false}
                sx={{
                  p: 4,
                  minWidth: { xs: 200, md: 250 },
                  cursor: 'pointer',
                  textAlign: 'center',
                  transition: 'all 0.4s ease',
                  '&:hover': {
                    transform: 'translateY(-8px) scale(1.02)',
                    boxShadow: '0 15px 40px rgba(212, 175, 55, 0.4), 0 0 30px rgba(212, 175, 55, 0.3)',
                  }
                }}
              >
                <Typography variant="h5" component="h3" sx={{ mb: 2, color: 'primary.main' }}>
                  Single Card
                </Typography>
                <Typography variant="body1" sx={{ color: 'text.secondary', fontStyle: 'italic' }}>
                  One card for focused guidance
                </Typography>
              </MysticalPaper>

              <MysticalPaper
                className="spread-option"
                onClick={() => handleSpreadSelection('three')}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                showDecorator={false}
                sx={{
                  p: 4,
                  minWidth: { xs: 200, md: 250 },
                  cursor: 'pointer',
                  textAlign: 'center',
                  transition: 'all 0.4s ease',
                  '&:hover': {
                    transform: 'translateY(-8px) scale(1.02)',
                    boxShadow: '0 15px 40px rgba(212, 175, 55, 0.4), 0 0 30px rgba(212, 175, 55, 0.3)',
                  }
                }}
              >
                <Typography variant="h5" component="h3" sx={{ mb: 2, color: 'primary.main' }}>
                  Three Card Spread
                </Typography>
                <Typography variant="body1" sx={{ color: 'text.secondary', fontStyle: 'italic' }}>
                  Past, Present, Future
                </Typography>
              </MysticalPaper>
            </Stack>
          </MysticalContainer>
        );

      case 'drawing':
        return (
          <MysticalContainer
            className="drawing-phase"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            maxWidth="md"
          >
            <Typography variant="h2" component="h2" align="center" sx={{ mb: 4, color: 'primary.main' }}>
              Drawing Your Cards...
            </Typography>
            <Box className="drawing-animation" sx={{ textAlign: 'center', fontSize: '5rem' }}>
              <motion.div
                initial={{ scale: 0.8 }}
                animate={{ scale: 1.1, rotate: 360 }}
                transition={{ duration: 1, ease: "easeInOut" }}
              >
                ✨
              </motion.div>
            </Box>
          </MysticalContainer>
        );

      case 'drawing-cards':
        const drawingPositions = getSpreadPositions(readingState.spreadType!);
        return (
          <MysticalContainer
            className="drawing-cards-phase"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            maxWidth="lg"
          >
            <Typography variant="h2" component="h2" align="center" sx={{ mb: 4, color: 'primary.main' }}>
              Your Cards
            </Typography>
            <Box
              sx={{
                width: '100%',
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                minHeight: '450px',
                mt: 3,
                mb: 3,
                p: 0,
              }}
            >
              <Box
                sx={{
                  display: 'flex',
                  flexDirection: readingState.spreadType === 'single' ? 'column' : 'row',
                  gap: readingState.spreadType === 'single' ? 0 : 4,
                  alignItems: 'center',
                  justifyContent: 'center',
                }}
              >
                {readingState.drawnCards.map((card, index) => (
                  <Box key={card.id} sx={{ display: 'flex', justifyContent: 'center' }}>
                    <Card
                      card={card}
                      onReveal={handleCardReveal}
                      position={drawingPositions[index]}
                      isDrawing={true}
                    />
                  </Box>
                ))}
              </Box>
            </Box>
          </MysticalContainer>
        );

      case 'reading':
      case 'complete':
        const positions = getSpreadPositions(readingState.spreadType!);
        return (
          <Box
            className="reading-phase"
            component={motion.div}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            sx={{
              width: '100%',
              minHeight: '60vh',
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              p: 0,
              m: 0,
            }}
          >
            <Typography variant="h2" component="h2" align="center" sx={{ mb: 4, color: 'primary.main' }}>
              Your Reading
            </Typography>

            {/* Main reading area with proper centering */}
            <Box
              sx={{
                width: '100%',
                flex: 1,
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center',
                gap: 4,
                p: 0,
                m: 0,
              }}
            >
              {/* Cards Container */}
              <Box
                sx={{
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'flex-start',
                  width: '100%',
                }}
              >
                {/* Cards Container */}
                <Box
                  sx={{
                    display: 'flex',
                    flexDirection: readingState.spreadType === 'single' ? 'column' : 'row',
                    gap: readingState.spreadType === 'single' ? 0 : 3,
                    alignItems: 'flex-start',
                    justifyContent: 'center',
                    width: '100%',
                  }}
                >
                  {readingState.drawnCards.map((card, index) => (
                    <Box key={card.id} sx={{ display: 'flex', justifyContent: 'center', flex: 1 }}>
                      <Card
                        card={card}
                        onReveal={handleCardReveal}
                        position={positions[index]}
                        isDrawing={false}
                      />
                    </Box>
                  ))}
                </Box>
              </Box>

              {/* Completion Message - positioned below the guidebook */}
              {readingState.phase === 'complete' && (
                <Box
                  sx={{
                    width: '100%',
                    display: 'flex',
                    justifyContent: 'center',
                    mt: 4,
                  }}
                >
                  <MysticalPaper
                    className="reading-complete"
                    component={motion.div}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.5 }}
                    sx={{
                      p: 3,
                      textAlign: 'center',
                      maxWidth: 600,
                    }}
                  >
                    <Typography variant="body1" sx={{ mb: 3, color: 'text.secondary', fontStyle: 'italic', fontSize: '1.2rem' }}>
                      Your reading is complete. Take time to reflect on the messages.
                    </Typography>
                    <MysticalButton
                      onClick={resetReading}
                      size="large"
                    >
                      New Reading
                    </MysticalButton>
                  </MysticalPaper>
                </Box>
              )}
            </Box>
          </Box>
        );

      default:
        return null;
    }
  };

  return (
    <Box
      className="oracle-reading"
      sx={{
        width: '100%',
        minHeight: '100%',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        position: 'relative',
        p: 0,
        m: 0,
      }}
    >
      <AnimatePresence mode="wait">
        {renderPhase()}
      </AnimatePresence>
    </Box>
  );
};
