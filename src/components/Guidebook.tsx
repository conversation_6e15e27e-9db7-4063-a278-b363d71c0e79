import React from 'react';
import { Box, Typography, Stack, Divider } from '@mui/material';
import { motion } from 'framer-motion';
import { DrawnCard } from '../types/Card';
import { colors } from '../theme/muiTheme';

interface GuidebookProps {
  card: DrawnCard;
  isVisible: boolean;
}

export const Guidebook: React.FC<GuidebookProps> = ({ card, isVisible }) => {
  if (!isVisible || !card.isRevealed) {
    return null;
  }

  return (
    <Box
      component={motion.div}
      initial={{ opacity: 0, x: 50 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{ duration: 0.6, ease: "easeOut" }}
      sx={{
        background: `linear-gradient(135deg, rgba(74, 44, 90, 0.4) 0%, rgba(26, 26, 46, 0.6) 100%)`,
        border: `2px solid ${colors.featherGold}`,
        borderRadius: '20px',
        boxShadow: `0 8px 32px ${colors.mysticalGlow}, inset 0 1px 0 rgba(245, 243, 240, 0.1)`,
        backdropFilter: 'blur(15px)',
        p: 3,
        position: 'relative',
        maxWidth: '400px',
        width: '100%',
        '&::before': {
          content: '"✦ Sacred Guidance ✦"',
          position: 'absolute',
          top: '-15px',
          left: '50%',
          transform: 'translateX(-50%)',
          background: colors.cosmicVoid,
          color: colors.featherGold,
          padding: '0 1rem',
          fontSize: '0.9rem',
          letterSpacing: '2px',
          fontFamily: '"Cinzel", serif',
          zIndex: 2,
        },
      }}
    >
      <Stack spacing={3}>
        {/* Card State Indicator */}
        <Box sx={{ textAlign: 'center' }}>
          <Typography
            variant="h6"
            sx={{
              fontFamily: '"Cinzel", serif',
              color: card.isReversed ? colors.bioluminescentBlue : colors.featherGold,
              fontSize: '1.1rem',
              letterSpacing: '1px',
              textShadow: `0 0 10px ${card.isReversed ? colors.bioluminescentBlue : colors.featherGold}`,
            }}
          >
            {card.isReversed ? 'Reversed' : 'Upright'}
          </Typography>
        </Box>

        <Divider
          sx={{
            borderColor: colors.featherGold,
            opacity: 0.5,
            '&::before, &::after': {
              borderColor: colors.featherGold,
            }
          }}
        />

        {/* Key Interpretation - shown for both upright and reversed */}
        <Box>
          <Typography
            variant="h6"
            sx={{
              fontFamily: '"Cinzel", serif',
              color: colors.featherGold,
              fontSize: '1.1rem',
              letterSpacing: '0.5px',
              mb: 1.5,
              textAlign: 'center',
              textShadow: `0 0 10px ${colors.featherGold}`,
            }}
          >
            Key Interpretation
          </Typography>
          <Typography
            variant="body1"
            sx={{
              fontFamily: '"Lora", serif',
              color: colors.etherealWhite,
              lineHeight: 1.7,
              fontSize: '1rem',
              textAlign: 'justify',
              fontWeight: 500,
              background: `linear-gradient(135deg, rgba(212, 175, 55, 0.1) 0%, rgba(100, 181, 246, 0.1) 100%)`,
              p: 2,
              borderRadius: '10px',
              border: `1px solid ${colors.featherGold}`,
              boxShadow: `0 0 15px rgba(212, 175, 55, 0.2)`,
            }}
          >
            {card.key_interpretation}
          </Typography>
        </Box>

        <Divider
          sx={{
            borderColor: colors.featherGold,
            opacity: 0.3,
            '&::before, &::after': {
              borderColor: colors.featherGold,
            }
          }}
        />

        {/* Content based on card state */}
        {card.isReversed ? (
          <Stack spacing={2.5}>
            {/* Reversal Information */}
            <Box>
              <Typography
                variant="h6"
                sx={{
                  fontFamily: '"Cinzel", serif',
                  color: colors.bioluminescentBlue,
                  fontSize: '1rem',
                  letterSpacing: '0.5px',
                  mb: 1.5,
                  textAlign: 'center',
                }}
              >
                Shadow Reflection
              </Typography>
              <Typography
                variant="body1"
                sx={{
                  fontFamily: '"Lora", serif',
                  color: colors.etherealWhite,
                  lineHeight: 1.7,
                  fontSize: '0.95rem',
                  textAlign: 'justify',
                }}
              >
                {card.reversal}
              </Typography>
            </Box>

            <Divider sx={{ borderColor: colors.bioluminescentBlue, opacity: 0.3 }} />

            {/* Reversal Meaning */}
            <Box>
              <Typography
                variant="h6"
                sx={{
                  fontFamily: '"Cinzel", serif',
                  color: colors.bioluminescentBlue,
                  fontSize: '1rem',
                  letterSpacing: '0.5px',
                  mb: 1.5,
                  textAlign: 'center',
                }}
              >
                Inner Inquiry
              </Typography>
              <Typography
                variant="body1"
                sx={{
                  fontFamily: '"Lora", serif',
                  color: colors.moonlightSilver,
                  lineHeight: 1.7,
                  fontSize: '0.95rem',
                  fontStyle: 'italic',
                  textAlign: 'justify',
                }}
              >
                {card.reversal_meaning}
              </Typography>
            </Box>
          </Stack>
        ) : (
          <Stack spacing={2.5}>
            {/* Meaning */}
            <Box>
              <Typography
                variant="h6"
                sx={{
                  fontFamily: '"Cinzel", serif',
                  color: colors.featherGold,
                  fontSize: '1rem',
                  letterSpacing: '0.5px',
                  mb: 1.5,
                  textAlign: 'center',
                }}
              >
                Sacred Meaning
              </Typography>
              <Typography
                variant="body1"
                sx={{
                  fontFamily: '"Lora", serif',
                  color: colors.etherealWhite,
                  lineHeight: 1.7,
                  fontSize: '0.95rem',
                  textAlign: 'justify',
                }}
              >
                {card.poetic_meaning}
              </Typography>
            </Box>

            <Divider sx={{ borderColor: colors.featherGold, opacity: 0.3 }} />

            {/* Journal Prompt */}
            <Box>
              <Typography
                variant="h6"
                sx={{
                  fontFamily: '"Cinzel", serif',
                  color: colors.featherGold,
                  fontSize: '1rem',
                  letterSpacing: '0.5px',
                  mb: 1.5,
                  textAlign: 'center',
                }}
              >
                Soul Inquiry
              </Typography>
              <Typography
                variant="body1"
                sx={{
                  fontFamily: '"Lora", serif',
                  color: colors.moonlightSilver,
                  lineHeight: 1.7,
                  fontSize: '0.95rem',
                  fontStyle: 'italic',
                  textAlign: 'justify',
                }}
              >
                {card.journal_prompt}
              </Typography>
            </Box>
          </Stack>
        )}
      </Stack>
    </Box>
  );
};
