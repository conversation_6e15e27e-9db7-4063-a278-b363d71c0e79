import React from 'react';
import { motion } from 'framer-motion';
import { Box, Typography, Stack } from '@mui/material';
import { DrawnCard } from '../types/Card';
import cardBackImage from '../assets/card-back.png';

interface CardProps {
  card: DrawnCard;
  onReveal: (cardId: string) => void;
  position: { x: number; y: number; label: string };
  isDrawing?: boolean;
}

export const Card: React.FC<CardProps> = ({ 
  card, 
  onReveal, 
  position, 
  isDrawing = false 
}) => {
  const handleClick = () => {
    if (!card.isRevealed && !isDrawing) {
      onReveal(card.id);
    }
  };

  return (
    <Stack
      className="card-container"
      spacing={2}
      alignItems="center"
      sx={{
        position: 'relative',
        margin: { xs: 1, md: 1.5 }
      }}
    >
      <Typography
        className="card-label"
        variant="h6"
        sx={{
          textAlign: 'center',
          fontFamily: '"Cinzel", serif',
          color: '#d4af37',
          fontWeight: 500,
          fontSize: { xs: '1rem', md: '1.2rem' },
          letterSpacing: '1px',
          textShadow: '0 0 10px rgba(212, 175, 55, 0.3)',
          mb: 1.5
        }}
      >
        {position.label}
      </Typography>

      <Box
        component={motion.div}
        className="card"
        onClick={handleClick}
        initial={isDrawing ? { scale: 0.8, opacity: 0 } : false}
        animate={isDrawing ? {
          scale: 1,
          opacity: 1,
          transition: { duration: 1, ease: "easeInOut" }
        } : {}}
        whileHover={!card.isRevealed && !isDrawing ? { scale: 1.05 } : {}}
        whileTap={!card.isRevealed && !isDrawing ? { scale: 0.95 } : {}}
        sx={{
          width: { xs: 320, sm: 360, md: 440 },
          height: { xs: 480, sm: 540, md: 660 },
          perspective: '1200px',
          cursor: !card.isRevealed && !isDrawing ? 'pointer' : 'default',
          position: 'relative',
        }}
      >
        <Box
          component={motion.div}
          className="card-inner"
          initial={false}
          animate={{ rotateY: card.isRevealed ? 180 : 0 }}
          transition={{ duration: 0.6, ease: "easeInOut" }}
          sx={{
            position: 'relative',
            width: '100%',
            height: '100%',
            transformStyle: 'preserve-3d',
            transition: 'transform 0.8s cubic-bezier(0.4, 0, 0.2, 1)',
          }}
        >
          {/* Card Back */}
          <Box
            className="card-face card-back"
            sx={{
              position: 'absolute',
              width: '100%',
              height: '100%',
              backfaceVisibility: 'hidden',
              borderRadius: '20px',
              overflow: 'hidden',
              boxShadow: '0 12px 35px rgba(10, 10, 15, 0.6), 0 0 0 3px #d4af37, inset 0 1px 0 rgba(245, 243, 240, 0.1)',
              background: 'linear-gradient(135deg, #2d1b3d 0%, #4a2c5a 50%, #1a1a2e 100%), radial-gradient(circle at center, rgba(212, 175, 55, 0.3) 0%, transparent 70%)',
              border: '3px solid #d4af37',
              display: 'flex',
              flexDirection: 'column',
              justifyContent: 'center',
              alignItems: 'center',
              '&::before': {
                content: '"✦ ◊ ✦"',
                position: 'absolute',
                top: '20px',
                left: '50%',
                transform: 'translateX(-50%)',
                color: '#d4af37',
                fontSize: '1.2rem',
                letterSpacing: '8px',
              },
              '&::after': {
                content: '"🌙"',
                position: 'absolute',
                bottom: '20px',
                left: '50%',
                transform: 'translateX(-50%)',
                fontSize: '2rem',
                filter: 'drop-shadow(0 0 10px rgba(212, 175, 55, 0.3))',
              },
            }}
          >
            <Box
              component="img"
              src={cardBackImage}
              alt="Card back"
              sx={{
                width: '75%',
                height: '75%',
                objectFit: 'cover',
                borderRadius: '15px',
                filter: 'sepia(30%) saturate(1.3) brightness(0.8) contrast(1.1)',
                border: '2px solid rgba(212, 175, 55, 0.3)',
              }}
            />
            {!card.isRevealed && (
              <Typography
                className="card-click-hint"
                sx={{
                  position: 'absolute',
                  bottom: '50px',
                  left: '50%',
                  transform: 'translateX(-50%)',
                  fontFamily: '"Lora", serif',
                  color: '#c9b8d4',
                  fontSize: '0.9rem',
                  fontStyle: 'italic',
                  opacity: 0.9,
                  textShadow: '0 0 8px rgba(212, 175, 55, 0.3)',
                  animation: 'gentlePulse 2s ease-in-out infinite',
                  '@keyframes gentlePulse': {
                    '0%, 100%': { opacity: 0.7 },
                    '50%': { opacity: 1 },
                  },
                }}
              >
                Click to reveal
              </Typography>
            )}
          </Box>

          {/* Card Front */}
          <Box
            className="card-face card-front"
            sx={{
              position: 'absolute',
              width: '100%',
              height: '100%',
              backfaceVisibility: 'hidden',
              borderRadius: '20px',
              overflow: 'hidden',
              background: 'linear-gradient(135deg, #f5f3f0 0%, #c9b8d4 100%), radial-gradient(circle at top right, rgba(212, 175, 55, 0.3) 0%, transparent 50%)',
              color: '#0a0a0f',
              transform: 'rotateY(180deg)',
              p: 1.5,
              display: 'flex',
              flexDirection: 'column',
              justifyContent: 'space-between',
              border: '2px solid #d4af37',
              boxShadow: '0 12px 35px rgba(10, 10, 15, 0.6), 0 0 0 3px #d4af37, inset 0 1px 0 rgba(245, 243, 240, 0.1)',
              '&::before': {
                content: '""',
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                background: 'radial-gradient(circle at 20% 20%, rgba(212, 175, 55, 0.3) 0%, transparent 30%), radial-gradient(circle at 80% 80%, rgba(100, 181, 246, 0.1) 0%, transparent 30%)',
                pointerEvents: 'none',
              },
            }}
          >
            <Stack
              className="card-content"
              spacing={4}
              sx={{
                height: '100%',
                justifyContent: 'center',
                alignItems: 'center',
                position: 'relative',
                zIndex: 1,
                p: 3,
              }}
            >
              {/* Card Title */}
              <Typography
                className="card-title"
                variant="h4"
                sx={{
                  fontFamily: '"Cinzel", serif',
                  fontSize: { xs: '1.8rem', md: '2.2rem' },
                  fontWeight: 600,
                  color: '#0a0a0f',
                  textAlign: 'center',
                  letterSpacing: '1.5px',
                  textShadow: '0 2px 4px rgba(212, 175, 55, 0.3)',
                  transform: card.isReversed ? 'rotate(180deg)' : 'none',
                  transition: 'transform 0.3s ease',
                }}
              >
                {card.title}
              </Typography>

              {/* Card Content - Affirmation or Reversal */}
              <Box
                className="card-content-text"
                sx={{
                  textAlign: 'center',
                  pt: 2,
                  borderTop: '3px solid #d4af37',
                  position: 'relative',
                  maxWidth: '90%',
                  '&::before': {
                    content: '"✦"',
                    position: 'absolute',
                    top: '-12px',
                    left: '50%',
                    transform: 'translateX(-50%)',
                    background: '#f5f3f0',
                    color: '#d4af37',
                    px: 1,
                    fontSize: '1rem',
                  },
                }}
              >
                <Typography
                  component="em"
                  variant="h6"
                  sx={{
                    fontFamily: '"Lora", serif',
                    fontSize: { xs: '1.1rem', md: '1.3rem' },
                    color: '#2d1b3d',
                    fontStyle: 'italic',
                    lineHeight: 1.6,
                    fontWeight: 500,
                  }}
                >
                  "{card.isReversed ? card.reversal : card.affirmation}"
                </Typography>
              </Box>
            </Stack>
          </Box>
        </Box>
      </Box>
    </Stack>
  );
};
