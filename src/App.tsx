import { Container, Typography, Box, Stack } from '@mui/material';
import { OracleReading } from './components/OracleReading';
import "./App.css";

function App() {
  return (
    <Box
      className="app"
      sx={{
        minHeight: '100vh',
        display: 'flex',
        flexDirection: 'column',
        position: 'relative',
      }}
    >
      <Box
        component="header"
        className="app-header"
        sx={{
          textAlign: 'center',
          py: { xs: 3, md: 4 },
          px: 2,
          background: `
            linear-gradient(135deg, rgba(74, 44, 90, 0.4) 0%, rgba(26, 26, 46, 0.6) 100%),
            radial-gradient(ellipse at center, rgba(212, 175, 55, 0.3) 0%, transparent 70%)
          `,
          backdropFilter: 'blur(15px)',
          borderBottom: '2px solid #d4af37',
          boxShadow: '0 4px 20px rgba(212, 175, 55, 0.2)',
          position: 'relative',
          '&::before': {
            content: '"✦"',
            position: 'absolute',
            top: '1rem',
            left: '50%',
            transform: 'translateX(-50%)',
            fontSize: '1.5rem',
            color: '#d4af37',
            opacity: 0.7,
          }
        }}
      >
        <Stack spacing={2} alignItems="center">
          <Typography
            variant="h1"
            component="h1"
            sx={{
              background: 'linear-gradient(135deg, #d4af37 0%, #64b5f6 50%, #d4af37 100%)',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              backgroundClip: 'text',
              textShadow: '0 0 30px rgba(212, 175, 55, 0.3)',
              letterSpacing: '2px',
              fontSize: { xs: '2.2rem', md: '3rem' }
            }}
          >
            The Descent of Falling Bird
          </Typography>
          <Typography
            variant="h6"
            component="p"
            sx={{
              fontStyle: 'italic',
              color: 'text.secondary',
              opacity: 0.9,
              letterSpacing: '1px',
              fontSize: { xs: '1rem', md: '1.1rem' }
            }}
          >
            Sacred Oracle of Shadow Work & Moonlit Wisdom
          </Typography>
        </Stack>
      </Box>

      <Box
        component="main"
        className="app-main"
        sx={{
          flex: 1,
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'flex-start',
          p: { xs: 2, md: 4 },
          position: 'relative',
          minHeight: 0, // Allow flex shrinking
          overflow: 'auto', // Handle content overflow
        }}
      >
        <Container
          maxWidth="xl"
          sx={{
            width: '100%',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            minHeight: '100%',
          }}
          data-testid="oracle-reading"
        >
          <OracleReading />
        </Container>
      </Box>
    </Box>
  );
}

export default App;
