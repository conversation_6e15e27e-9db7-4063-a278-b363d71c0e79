:root {
  /* Cosmic-Tribal Fusion Typography */
  --font-title: '<PERSON><PERSON><PERSON>', serif;
  --font-body: '<PERSON><PERSON>', serif;

  /* Dark & Dreamy Color Palette */
  --cosmic-void: #0a0a0f;
  --shadow-deep: #1a1a2e;
  --tribal-earth: #2d1b3d;
  --sacred-purple: #4a2c5a;
  --moonlight-silver: #c9b8d4;
  --feather-gold: #d4af37;
  --bioluminescent-blue: #64b5f6;
  --ethereal-white: #f5f3f0;
  --mystical-glow: rgba(212, 175, 55, 0.3);

  font-family: var(--font-body);
  font-size: 16px;
  line-height: 1.7;
  font-weight: 400;

  color: var(--moonlight-silver);
  background:
    radial-gradient(ellipse at top, var(--tribal-earth) 0%, var(--shadow-deep) 40%, var(--cosmic-void) 100%),
    linear-gradient(135deg, var(--cosmic-void) 0%, var(--shadow-deep) 50%, var(--tribal-earth) 100%);
  min-height: 100vh;
  overflow-x: hidden;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-text-size-adjust: 100%;
}

/* Sacred Geometry Background Pattern */
body {
  margin: 0;
  padding: 0;
  overflow-x: hidden;
  position: relative;
}

body::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    radial-gradient(circle at 20% 30%, var(--mystical-glow) 0%, transparent 50%),
    radial-gradient(circle at 80% 70%, rgba(100, 181, 246, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 80%, var(--mystical-glow) 0%, transparent 30%);
  pointer-events: none;
  z-index: -1;
}

/* Oracle Reading Sacred Container - now handled by MUI */
.oracle-reading {
  /* Styles moved to MUI sx props */
}

/* Question Phase - Sacred Meditation Space - now handled by MUI */

.question-area {
  background:
    linear-gradient(135deg, rgba(74, 44, 90, 0.3) 0%, rgba(26, 26, 46, 0.5) 100%);
  padding: 3rem;
  border-radius: 20px;
  border: 2px solid var(--feather-gold);
  box-shadow:
    0 8px 32px rgba(212, 175, 55, 0.2),
    inset 0 1px 0 rgba(245, 243, 240, 0.1);
  margin-top: 2rem;
  backdrop-filter: blur(10px);
  position: relative;
}

.question-area::before {
  content: '🌙';
  position: absolute;
  top: -15px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 2rem;
  background: var(--cosmic-void);
  padding: 0 1rem;
}

.question-area p {
  font-family: var(--font-body);
  font-size: 1.2rem;
  line-height: 1.8;
  color: var(--moonlight-silver);
  margin-bottom: 2rem;
}

/* Sacred Buttons with Tribal Patterns */
.continue-button,
.shuffle-button,
.new-reading-button {
  font-family: var(--font-title);
  background:
    linear-gradient(135deg, var(--sacred-purple) 0%, var(--tribal-earth) 100%);
  color: var(--ethereal-white);
  border: 2px solid var(--feather-gold);
  border-radius: 15px;
  padding: 1.2rem 2.5rem;
  font-size: 1.1rem;
  font-weight: 500;
  letter-spacing: 1px;
  cursor: pointer;
  transition: all 0.4s ease;
  margin-top: 1.5rem;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(212, 175, 55, 0.3);
}

.continue-button::before,
.shuffle-button::before,
.new-reading-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, var(--mystical-glow), transparent);
  transition: left 0.6s ease;
}

.continue-button:hover,
.shuffle-button:hover,
.new-reading-button:hover {
  background:
    linear-gradient(135deg, var(--feather-gold) 0%, var(--bioluminescent-blue) 100%);
  color: var(--cosmic-void);
  box-shadow:
    0 0 30px var(--mystical-glow),
    0 8px 25px rgba(212, 175, 55, 0.4);
  transform: translateY(-3px);
}

.continue-button:hover::before,
.shuffle-button:hover::before,
.new-reading-button:hover::before {
  left: 100%;
}

/* Sacred Deck Styles - now handled by MUI */

/* Selection Phase - Sacred Choice - now handled by MUI */

/* Drawing Phase - Mystical Transition - now handled by MUI */

@keyframes mysticalPulse {
  0%, 100% {
    transform: scale(1) rotate(0deg);
    filter: drop-shadow(0 0 20px var(--mystical-glow));
  }
  50% {
    transform: scale(1.2) rotate(180deg);
    filter: drop-shadow(0 0 40px var(--feather-gold));
  }
}

/* Drawing Cards Phase - now handled by MUI */

/* Reading Phase - Sacred Revelation - now handled by MUI */

/* Sacred Card Styles - Cosmic-Tribal Fusion - now handled by MUI */

/* Card animations - keep for MUI components */
@keyframes gentlePulse {
  0%, 100% { opacity: 0.7; }
  50% { opacity: 1; }
}

/* Card styles - now handled by MUI */

/* Reading Complete - Sacred Closure - now handled by MUI */

/* Responsive Design - now handled by MUI responsive system */
