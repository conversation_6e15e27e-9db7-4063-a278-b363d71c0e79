import { OracleCard, DrawnCard, ReadingState, SpreadType } from '../../types/Card';

describe('Card Types', () => {
  describe('OracleCard', () => {
    test('should have correct structure', () => {
      const oracleCard: OracleCard = {
        title: 'Test Card',
        suite: 'Test Suite',
        poetic_meaning: 'Test meaning',
        affirmation: 'Test affirmation',
        journal_prompt: 'Test journal prompt',
        reversal: 'Test reversal',
        reversal_meaning: 'Test reversal meaning',
        key_interpretation: 'Test key interpretation'
      };

      expect(oracleCard.title).toBe('Test Card');
      expect(oracleCard.suite).toBe('Test Suite');
      expect(oracleCard.poetic_meaning).toBe('Test meaning');
      expect(oracleCard.affirmation).toBe('Test affirmation');
      expect(oracleCard.journal_prompt).toBe('Test journal prompt');
      expect(oracleCard.reversal).toBe('Test reversal');
      expect(oracleCard.reversal_meaning).toBe('Test reversal meaning');
      expect(oracleCard.key_interpretation).toBe('Test key interpretation');
    });
  });

  describe('DrawnCard', () => {
    test('should extend OracleCard with additional properties', () => {
      const drawnCard: DrawnCard = {
        id: 'test-id',
        title: 'Test Card',
        suite: 'Test Suite',
        poetic_meaning: 'Test meaning',
        affirmation: 'Test affirmation',
        journal_prompt: 'Test journal prompt',
        reversal: 'Test reversal',
        reversal_meaning: 'Test reversal meaning',
        key_interpretation: 'Test key interpretation',
        isRevealed: false,
        isReversed: false,
        position: 0
      };

      expect(drawnCard.isRevealed).toBe(false);
      expect(drawnCard.isReversed).toBe(false);
      expect(drawnCard.position).toBe(0);
      expect(drawnCard.id).toBe('test-id');
      expect(drawnCard.title).toBe('Test Card');
    });

    test('should allow isRevealed to be true', () => {
      const drawnCard: DrawnCard = {
        id: 'test-id',
        title: 'Test Card',
        suite: 'Test Suite',
        poetic_meaning: 'Test meaning',
        affirmation: 'Test affirmation',
        journal_prompt: 'Test journal prompt',
        reversal: 'Test reversal',
        reversal_meaning: 'Test reversal meaning',
        key_interpretation: 'Test key interpretation',
        isRevealed: true,
        isReversed: true,
        position: 1
      };

      expect(drawnCard.isRevealed).toBe(true);
      expect(drawnCard.isReversed).toBe(true);
      expect(drawnCard.position).toBe(1);
    });
  });

  describe('SpreadType', () => {
    test('should accept valid spread types', () => {
      const singleSpread: SpreadType = 'single';
      const threeSpread: SpreadType = 'three';

      expect(singleSpread).toBe('single');
      expect(threeSpread).toBe('three');
    });
  });

  describe('ReadingState', () => {
    test('should have correct structure for initial state', () => {
      const readingState: ReadingState = {
        phase: 'question',
        spreadType: null,
        drawnCards: [],
        isShuffling: false,
        deckShuffled: false
      };

      expect(readingState.phase).toBe('question');
      expect(readingState.spreadType).toBeNull();
      expect(readingState.drawnCards).toEqual([]);
      expect(readingState.isShuffling).toBe(false);
      expect(readingState.deckShuffled).toBe(false);
    });

    test('should have correct structure for drawing state', () => {
      const readingState: ReadingState = {
        phase: 'drawing',
        spreadType: 'three',
        drawnCards: [],
        isShuffling: false,
        deckShuffled: true
      };

      expect(readingState.phase).toBe('drawing');
      expect(readingState.spreadType).toBe('three');
      expect(readingState.deckShuffled).toBe(true);
    });

    test('should have correct structure for reading state', () => {
      const drawnCards: DrawnCard[] = [
        {
          id: '1',
          title: 'Card 1',
          suite: 'Test Suite',
          poetic_meaning: 'Test meaning',
          affirmation: 'Test affirmation',
          image: 'image1.jpg',
          isRevealed: false,
          position: 0
        }
      ];

      const readingState: ReadingState = {
        phase: 'reading',
        spreadType: 'single',
        drawnCards,
        isShuffling: false,
        deckShuffled: true
      };

      expect(readingState.phase).toBe('reading');
      expect(readingState.spreadType).toBe('single');
      expect(readingState.drawnCards).toHaveLength(1);
    });

    test('should have correct structure for complete state', () => {
      const drawnCards: DrawnCard[] = [
        {
          id: '1',
          title: 'Card 1',
          suite: 'Test Suite',
          poetic_meaning: 'Test meaning',
          affirmation: 'Test affirmation',
          image: 'image1.jpg',
          isRevealed: true,
          position: 0
        }
      ];

      const readingState: ReadingState = {
        phase: 'complete',
        spreadType: 'single',
        drawnCards,
        isShuffling: false,
        deckShuffled: true
      };

      expect(readingState.phase).toBe('complete');
      expect(readingState.drawnCards[0].isRevealed).toBe(true);
    });
  });
});
