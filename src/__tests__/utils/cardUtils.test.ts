import { drawCards, getCardCount, getSpreadPositions, sleep, shuffleArray } from '../../utils/cardUtils';
import { SpreadType, OracleCard } from '../../types/Card';

// Mock oracle cards for testing
const mockCards: OracleCard[] = [
  {
    title: 'Test Card 1',
    suite: 'Test Suite',
    poetic_meaning: 'Test meaning 1',
    affirmation: 'Test affirmation 1',
    image: 'image1.jpg'
  },
  {
    title: 'Test Card 2',
    suite: 'Test Suite',
    poetic_meaning: 'Test meaning 2',
    affirmation: 'Test affirmation 2',
    image: 'image2.jpg'
  },
  {
    title: 'Test Card 3',
    suite: 'Test Suite',
    poetic_meaning: 'Test meaning 3',
    affirmation: 'Test affirmation 3',
    image: 'image3.jpg'
  }
];

describe('cardUtils', () => {
  describe('shuffleArray', () => {
    test('returns array with same length', () => {
      const result = shuffleArray(mockCards);
      expect(result).toHaveLength(mockCards.length);
    });

    test('returns array with same elements', () => {
      const result = shuffleArray(mockCards);
      expect(result).toEqual(expect.arrayContaining(mockCards));
    });

    test('does not mutate original array', () => {
      const original = [...mockCards];
      shuffleArray(mockCards);
      expect(mockCards).toEqual(original);
    });
  });

  describe('getCardCount', () => {
    test('returns 1 for single spread', () => {
      expect(getCardCount('single')).toBe(1);
    });

    test('returns 3 for three spread', () => {
      expect(getCardCount('three')).toBe(3);
    });
  });

  describe('getSpreadPositions', () => {
    test('returns correct positions for single spread', () => {
      const positions = getSpreadPositions('single');
      expect(positions).toHaveLength(1);
      expect(positions[0]).toEqual({
        x: 0,
        y: 0,
        label: 'Your Card'
      });
    });

    test('returns correct positions for three spread', () => {
      const positions = getSpreadPositions('three');
      expect(positions).toHaveLength(3);
      expect(positions[0].label).toBe('Past');
      expect(positions[1].label).toBe('Present');
      expect(positions[2].label).toBe('Future');
    });
  });

  describe('drawCards', () => {
    test('draws correct number of cards', () => {
      const cards = drawCards(mockCards, 2);
      expect(cards).toHaveLength(2);
      cards.forEach(card => {
        expect(card).toHaveProperty('id');
        expect(card).toHaveProperty('title');
        expect(card).toHaveProperty('isRevealed', false);
        expect(card).toHaveProperty('position');
      });
    });

    test('draws single card', () => {
      const cards = drawCards(mockCards, 1);
      expect(cards).toHaveLength(1);
      expect(cards[0]).toHaveProperty('id');
      expect(cards[0]).toHaveProperty('title');
      expect(cards[0]).toHaveProperty('isRevealed', false);
    });

    test('draws all available cards when count exceeds deck size', () => {
      const cards = drawCards(mockCards, 10);
      expect(cards).toHaveLength(mockCards.length);
    });

    test('assigns unique IDs to cards', () => {
      const cards = drawCards(mockCards, 3);
      const ids = cards.map(card => card.id);
      const uniqueIds = [...new Set(ids)];
      expect(uniqueIds).toHaveLength(ids.length);
    });

    test('assigns correct positions', () => {
      const cards = drawCards(mockCards, 3);
      cards.forEach((card, index) => {
        expect(card.position).toBe(index);
      });
    });
  });

  describe('sleep', () => {
    test('resolves after specified time', async () => {
      const start = Date.now();
      await sleep(100);
      const end = Date.now();
      expect(end - start).toBeGreaterThanOrEqual(90); // Allow some tolerance
    });

    test('resolves immediately for 0 ms', async () => {
      const start = Date.now();
      await sleep(0);
      const end = Date.now();
      expect(end - start).toBeLessThan(50);
    });
  });
});
