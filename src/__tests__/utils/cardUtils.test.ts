import { drawCards, getCardCount, getSpreadPositions, sleep, shuffleArray } from '../../utils/cardUtils';
import { SpreadType, OracleCard } from '../../types/Card';

// Mock oracle cards for testing
const mockCards: OracleCard[] = [
  {
    title: 'Test Card 1',
    suite: 'Test Suite',
    upright_meaning: 'Test upright meaning 1',
    upright_key_interpretation: 'Test upright interpretation 1',
    upright_affirmation: 'Test upright affirmation 1',
    upright_journal_prompt: 'Test upright prompt 1',
    reversal_key_interpretation: 'Test reversal interpretation 1',
    reversal_journal_prompt: 'Test reversal prompt 1',
    reversal_meaning: 'Test reversal meaning 1',
    reversal_affirmation: 'Test reversal affirmation 1'
  },
  {
    title: 'Test Card 2',
    suite: 'Test Suite',
    upright_meaning: 'Test upright meaning 2',
    upright_key_interpretation: 'Test upright interpretation 2',
    upright_affirmation: 'Test upright affirmation 2',
    upright_journal_prompt: 'Test upright prompt 2',
    reversal_key_interpretation: 'Test reversal interpretation 2',
    reversal_journal_prompt: 'Test reversal prompt 2',
    reversal_meaning: 'Test reversal meaning 2',
    reversal_affirmation: 'Test reversal affirmation 2'
  },
  {
    title: 'Test Card 3',
    suite: 'Test Suite',
    upright_meaning: 'Test upright meaning 3',
    upright_key_interpretation: 'Test upright interpretation 3',
    upright_affirmation: 'Test upright affirmation 3',
    upright_journal_prompt: 'Test upright prompt 3',
    reversal_key_interpretation: 'Test reversal interpretation 3',
    reversal_journal_prompt: 'Test reversal prompt 3',
    reversal_meaning: 'Test reversal meaning 3',
    reversal_affirmation: 'Test reversal affirmation 3'
  }
];

describe('cardUtils', () => {
  describe('shuffleArray', () => {
    test('returns array with same length', () => {
      const result = shuffleArray(mockCards);
      expect(result).toHaveLength(mockCards.length);
    });

    test('returns array with same elements', () => {
      const result = shuffleArray(mockCards);
      expect(result).toEqual(expect.arrayContaining(mockCards));
    });

    test('does not mutate original array', () => {
      const original = [...mockCards];
      shuffleArray(mockCards);
      expect(mockCards).toEqual(original);
    });
  });

  describe('getCardCount', () => {
    test('returns 1 for single spread', () => {
      expect(getCardCount('single')).toBe(1);
    });

    test('returns 3 for three spread', () => {
      expect(getCardCount('three')).toBe(3);
    });
  });

  describe('getSpreadPositions', () => {
    test('returns correct positions for single spread', () => {
      const positions = getSpreadPositions('single');
      expect(positions).toHaveLength(1);
      expect(positions[0]).toEqual({
        x: 0,
        y: 0,
        label: 'Your Card'
      });
    });

    test('returns correct positions for three spread', () => {
      const positions = getSpreadPositions('three');
      expect(positions).toHaveLength(3);
      expect(positions[0].label).toBe('Past');
      expect(positions[1].label).toBe('Present');
      expect(positions[2].label).toBe('Future');
    });
  });

  describe('drawCards', () => {
    test('draws correct number of cards', () => {
      const cards = drawCards(mockCards, 2);
      expect(cards).toHaveLength(2);
      cards.forEach(card => {
        expect(card).toHaveProperty('id');
        expect(card).toHaveProperty('title');
        expect(card).toHaveProperty('isRevealed', false);
        expect(card).toHaveProperty('position');
      });
    });

    test('draws single card', () => {
      const cards = drawCards(mockCards, 1);
      expect(cards).toHaveLength(1);
      expect(cards[0]).toHaveProperty('id');
      expect(cards[0]).toHaveProperty('title');
      expect(cards[0]).toHaveProperty('isRevealed', false);
    });

    test('draws all available cards when count exceeds deck size', () => {
      const cards = drawCards(mockCards, 10);
      expect(cards).toHaveLength(mockCards.length);
    });

    test('assigns unique IDs to cards', () => {
      const cards = drawCards(mockCards, 3);
      const ids = cards.map(card => card.id);
      const uniqueIds = [...new Set(ids)];
      expect(uniqueIds).toHaveLength(ids.length);
    });

    test('assigns correct positions', () => {
      const cards = drawCards(mockCards, 3);
      cards.forEach((card, index) => {
        expect(card.position).toBe(index);
      });
    });
  });

  describe('sleep', () => {
    test('resolves after specified time', async () => {
      const start = Date.now();
      await sleep(100);
      const end = Date.now();
      expect(end - start).toBeGreaterThanOrEqual(90); // Allow some tolerance
    });

    test('resolves immediately for 0 ms', async () => {
      const start = Date.now();
      await sleep(0);
      const end = Date.now();
      expect(end - start).toBeLessThan(50);
    });
  });
});
