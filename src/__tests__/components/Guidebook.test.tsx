import React from 'react';
import { render, screen } from '@testing-library/react';
import { ThemeProvider } from '@mui/material/styles';
import { Guidebook } from '../../components/Guidebook';
import { fallingBirdTheme } from '../../theme/muiTheme';
import { DrawnCard } from '../../types/Card';

const renderWithTheme = (component: React.ReactElement) => {
  return render(
    <ThemeProvider theme={fallingBirdTheme}>
      {component}
    </ThemeProvider>
  );
};

const mockCard: DrawnCard = {
  id: 'test-card',
  title: 'Test Card',
  suite: 'Test Suite',
  poetic_meaning: 'This is a test meaning for the card.',
  affirmation: 'I am testing this card.',
  journal_prompt: 'What does this test mean to you?',
  reversal: 'This card is reversed and shows challenges.',
  reversal_meaning: 'How can you overcome these test challenges?',
  key_interpretation: 'This is the key interpretation for testing.',
  isRevealed: true,
  isReversed: false,
  position: 0,
};

describe('Guidebook', () => {
  test('should not render when card is not revealed', () => {
    const unrevealedCard = { ...mockCard, isRevealed: false };
    renderWithTheme(<Guidebook card={unrevealedCard} isVisible={true} />);

    expect(screen.queryByText('Upright')).not.toBeInTheDocument();
  });

  test('should not render when isVisible is false', () => {
    renderWithTheme(<Guidebook card={mockCard} isVisible={false} />);

    expect(screen.queryByText('Upright')).not.toBeInTheDocument();
  });

  test('should render upright card content correctly', () => {
    renderWithTheme(<Guidebook card={mockCard} isVisible={true} />);

    expect(screen.getByText('Upright')).toBeInTheDocument();
    expect(screen.getByText('Key Interpretation')).toBeInTheDocument();
    expect(screen.getByText('Sacred Meaning')).toBeInTheDocument();
    expect(screen.getByText('Soul Inquiry')).toBeInTheDocument();
    expect(screen.getByText(mockCard.key_interpretation)).toBeInTheDocument();
    expect(screen.getByText(mockCard.poetic_meaning)).toBeInTheDocument();
    expect(screen.getByText(mockCard.journal_prompt)).toBeInTheDocument();

    // Should not show reversed content
    expect(screen.queryByText('Shadow Reflection')).not.toBeInTheDocument();
    expect(screen.queryByText('Inner Inquiry')).not.toBeInTheDocument();
  });

  test('should render reversed card content correctly', () => {
    const reversedCard = { ...mockCard, isReversed: true };
    renderWithTheme(<Guidebook card={reversedCard} isVisible={true} />);

    expect(screen.getByText('Reversed')).toBeInTheDocument();
    expect(screen.getByText('Key Interpretation')).toBeInTheDocument();
    expect(screen.getByText('Shadow Reflection')).toBeInTheDocument();
    expect(screen.getByText('Inner Inquiry')).toBeInTheDocument();
    expect(screen.getByText(mockCard.key_interpretation)).toBeInTheDocument();
    expect(screen.getByText(mockCard.reversal)).toBeInTheDocument();
    expect(screen.getByText(mockCard.reversal_meaning)).toBeInTheDocument();

    // Should not show upright content
    expect(screen.queryByText('Sacred Meaning')).not.toBeInTheDocument();
    expect(screen.queryByText('Soul Inquiry')).not.toBeInTheDocument();
  });

  test('should have proper styling and structure', () => {
    renderWithTheme(<Guidebook card={mockCard} isVisible={true} />);

    const guidebook = screen.getByText('Upright').closest('div');
    expect(guidebook).toBeInTheDocument();
  });
});
