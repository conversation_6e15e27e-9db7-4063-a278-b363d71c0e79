import React from 'react';
import { render, screen } from '@testing-library/react';
import { ThemeProvider } from '@mui/material/styles';
import { Card } from '../../components/Card';
import { DrawnCard } from '../../types/Card';
import { fallingBirdTheme } from '../../theme/muiTheme';

const renderWithTheme = (component: React.ReactElement) => {
  return render(
    <ThemeProvider theme={fallingBirdTheme}>
      {component}
    </ThemeProvider>
  );
};

const mockCard: DrawnCard = {
  id: 'test-card-1',
  title: 'Test Card',
  suite: 'TEST SUITE',
  upright_meaning: 'Test upright meaning',
  upright_key_interpretation: 'Test upright interpretation',
  upright_affirmation: 'I am testing affirmations',
  upright_journal_prompt: 'Test upright journal prompt',
  reversal_key_interpretation: 'Test reversal interpretation',
  reversal_journal_prompt: 'Test reversal journal prompt',
  reversal_meaning: 'Test reversal meaning',
  reversal_affirmation: 'I am testing reversals',
  isRevealed: true,
  isReversed: false,
  position: 0
};

const mockPosition = { x: 0, y: 0, label: 'Test Position' };
const mockOnReveal = jest.fn();

describe('Card Reversal Logic', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('displays affirmation text when card is upright', () => {
    const uprightCard = { ...mockCard, isReversed: false };
    
    renderWithTheme(
      <Card
        card={uprightCard}
        onReveal={mockOnReveal}
        position={mockPosition}
        isDrawing={false}
      />
    );

    // Should display the affirmation text
    expect(screen.getByText('"I am testing affirmations"')).toBeInTheDocument();
    // Should NOT display the reversal text
    expect(screen.queryByText('"I am testing reversals"')).not.toBeInTheDocument();
  });

  test('displays reversal text when card is reversed', () => {
    const reversedCard = { ...mockCard, isReversed: true };
    
    renderWithTheme(
      <Card
        card={reversedCard}
        onReveal={mockOnReveal}
        position={mockPosition}
        isDrawing={false}
      />
    );

    // Should display the reversal text
    expect(screen.getByText('"I am testing reversals"')).toBeInTheDocument();
    // Should NOT display the affirmation text
    expect(screen.queryByText('"I am testing affirmations"')).not.toBeInTheDocument();
  });

  test('card title is rotated when reversed', () => {
    const reversedCard = { ...mockCard, isReversed: true };
    
    const { container } = renderWithTheme(
      <Card
        card={reversedCard}
        onReveal={mockOnReveal}
        position={mockPosition}
        isDrawing={false}
      />
    );

    const titleElement = container.querySelector('.card-title');
    expect(titleElement).toHaveStyle('transform: rotate(180deg)');
  });

  test('card title is not rotated when upright', () => {
    const uprightCard = { ...mockCard, isReversed: false };
    
    const { container } = renderWithTheme(
      <Card
        card={uprightCard}
        onReveal={mockOnReveal}
        position={mockPosition}
        isDrawing={false}
      />
    );

    const titleElement = container.querySelector('.card-title');
    expect(titleElement).toHaveStyle('transform: none');
  });

  test('displays card back when not revealed', () => {
    const hiddenCard = { ...mockCard, isRevealed: false };

    renderWithTheme(
      <Card
        card={hiddenCard}
        onReveal={mockOnReveal}
        position={mockPosition}
        isDrawing={false}
      />
    );

    // Should show card back image and click hint
    expect(screen.getByAltText('Card back')).toBeInTheDocument();
    expect(screen.getByText('Click to reveal')).toBeInTheDocument();

    // Should not show guidance button when not revealed
    expect(screen.queryByText('✦ Show Guidance ✦')).not.toBeInTheDocument();
  });
});
