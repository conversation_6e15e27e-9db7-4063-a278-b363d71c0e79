import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { ThemeProvider } from '@mui/material/styles';
import { OracleReading } from '../../components/OracleReading';
import { fallingBirdTheme } from '../../theme/muiTheme';

// Mock the card utilities
jest.mock('../../utils/cardUtils', () => ({
  drawCards: jest.fn((spreadType) => {
    const counts = { 'single': 1, 'three-card': 3, 'five-card': 5 };
    const count = counts[spreadType] || 1;
    return Array.from({ length: count }, (_, i) => ({
      id: `card-${i}`,
      title: `Test Card ${i + 1}`,
      description: `Description ${i + 1}`,
      keywords: [`keyword${i + 1}`],
      image: `image${i + 1}.jpg`,
      isRevealed: false,
    }));
  }),
  getCardCount: jest.fn((spreadType) => {
    const counts = { 'single': 1, 'three-card': 3, 'five-card': 5 };
    return counts[spreadType] || 1;
  }),
  getSpreadPositions: jest.fn((spreadType) => {
    const positions = {
      'single': [{ x: 0, y: 0, label: 'Your Message' }],
      'three-card': [
        { x: -150, y: 0, label: 'Past' },
        { x: 0, y: 0, label: 'Present' },
        { x: 150, y: 0, label: 'Future' }
      ],
      'five-card': [
        { x: 0, y: -100, label: 'Situation' },
        { x: -150, y: 0, label: 'Challenge' },
        { x: -75, y: 100, label: 'Past' },
        { x: 75, y: 100, label: 'Future' },
        { x: 150, y: 0, label: 'Outcome' }
      ]
    };
    return positions[spreadType] || positions['single'];
  }),
  sleep: jest.fn(() => Promise.resolve()),
}));

// Mock the components
jest.mock('../../components/Deck', () => ({
  Deck: ({ onCardDraw }: { onCardDraw: () => void }) => (
    <div data-testid="deck" onClick={onCardDraw}>
      Deck Component
    </div>
  ),
}));

jest.mock('../../components/Card', () => ({
  Card: ({ card, onReveal }: any) => (
    <div 
      data-testid={`card-${card.id}`}
      onClick={() => onReveal(card.id)}
    >
      {card.isRevealed ? card.title : 'Card Back'}
    </div>
  ),
}));

const renderWithTheme = (component: React.ReactElement) => {
  return render(
    <ThemeProvider theme={fallingBirdTheme}>
      {component}
    </ThemeProvider>
  );
};

describe('OracleReading Component', () => {
  test('renders spread selection initially', () => {
    renderWithTheme(<OracleReading />);
    
    expect(screen.getByText('Choose Your Spread')).toBeInTheDocument();
    expect(screen.getByText('Single Card')).toBeInTheDocument();
    expect(screen.getByText('Three Card Spread')).toBeInTheDocument();
    expect(screen.getByText('Five Card Spread')).toBeInTheDocument();
  });

  test('transitions to drawing phase when spread is selected', async () => {
    renderWithTheme(<OracleReading />);
    
    const singleCardButton = screen.getByText('Single Card');
    fireEvent.click(singleCardButton);
    
    await waitFor(() => {
      expect(screen.getByText('Draw Your Cards')).toBeInTheDocument();
    });
  });

  test('shows deck in drawing phase', async () => {
    renderWithTheme(<OracleReading />);
    
    const threeCardButton = screen.getByText('Three Card Spread');
    fireEvent.click(threeCardButton);
    
    await waitFor(() => {
      expect(screen.getByTestId('deck')).toBeInTheDocument();
    });
  });

  test('draws cards when deck is clicked', async () => {
    renderWithTheme(<OracleReading />);
    
    const singleCardButton = screen.getByText('Single Card');
    fireEvent.click(singleCardButton);
    
    await waitFor(() => {
      const deck = screen.getByTestId('deck');
      fireEvent.click(deck);
    });
    
    await waitFor(() => {
      expect(screen.getByText('Your Reading')).toBeInTheDocument();
    });
  });

  test('reveals cards when clicked', async () => {
    renderWithTheme(<OracleReading />);
    
    // Select spread
    const singleCardButton = screen.getByText('Single Card');
    fireEvent.click(singleCardButton);
    
    // Draw cards
    await waitFor(() => {
      const deck = screen.getByTestId('deck');
      fireEvent.click(deck);
    });
    
    // Click on card to reveal
    await waitFor(() => {
      const card = screen.getByTestId('card-card-0');
      fireEvent.click(card);
    });
    
    await waitFor(() => {
      expect(screen.getByText('Test Card 1')).toBeInTheDocument();
    });
  });

  test('shows completion message when all cards are revealed', async () => {
    renderWithTheme(<OracleReading />);
    
    // Select single card spread
    const singleCardButton = screen.getByText('Single Card');
    fireEvent.click(singleCardButton);
    
    // Draw cards
    await waitFor(() => {
      const deck = screen.getByTestId('deck');
      fireEvent.click(deck);
    });
    
    // Reveal the card
    await waitFor(() => {
      const card = screen.getByTestId('card-card-0');
      fireEvent.click(card);
    });
    
    // Check for completion
    await waitFor(() => {
      expect(screen.getByText('Your reading is complete. Take time to reflect on the messages.')).toBeInTheDocument();
      expect(screen.getByText('New Reading')).toBeInTheDocument();
    });
  });

  test('resets reading when New Reading button is clicked', async () => {
    renderWithTheme(<OracleReading />);
    
    // Complete a reading
    const singleCardButton = screen.getByText('Single Card');
    fireEvent.click(singleCardButton);
    
    await waitFor(() => {
      const deck = screen.getByTestId('deck');
      fireEvent.click(deck);
    });
    
    await waitFor(() => {
      const card = screen.getByTestId('card-card-0');
      fireEvent.click(card);
    });
    
    // Click New Reading
    await waitFor(() => {
      const newReadingButton = screen.getByText('New Reading');
      fireEvent.click(newReadingButton);
    });
    
    // Should be back to selection
    await waitFor(() => {
      expect(screen.getByText('Choose Your Spread')).toBeInTheDocument();
    });
  });

  test('handles three-card spread correctly', async () => {
    renderWithTheme(<OracleReading />);
    
    const threeCardButton = screen.getByText('Three Card Spread');
    fireEvent.click(threeCardButton);
    
    await waitFor(() => {
      const deck = screen.getByTestId('deck');
      fireEvent.click(deck);
    });
    
    await waitFor(() => {
      expect(screen.getByTestId('card-card-0')).toBeInTheDocument();
      expect(screen.getByTestId('card-card-1')).toBeInTheDocument();
      expect(screen.getByTestId('card-card-2')).toBeInTheDocument();
    });
  });
});
