import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { ThemeProvider } from '@mui/material/styles';
import { Deck } from '../../components/Deck';
import { fallingBirdTheme } from '../../theme/muiTheme';

const renderWithTheme = (component: React.ReactElement) => {
  return render(
    <ThemeProvider theme={fallingBirdTheme}>
      {component}
    </ThemeProvider>
  );
};

describe('Deck Component', () => {

  test('renders deck without shuffle button', () => {
    renderWithTheme(
      <Deck
        cardCount={44}
        isShuffling={false}
      />
    );

    expect(screen.queryByText('Shuffle Deck')).not.toBeInTheDocument();
  });

  test('renders deck cards correctly', () => {
    renderWithTheme(
      <Deck
        cardCount={44}
        isShuffling={false}
      />
    );

    const deckContainer = screen.getByTestId('deck-container');
    expect(deckContainer).toBeInTheDocument();
  });

  test('applies shuffling animation when shuffling', () => {
    renderWithTheme(
      <Deck
        cardCount={44}
        isShuffling={true}
      />
    );

    const deckContainer = screen.getByTestId('deck-container');
    expect(deckContainer).toBeInTheDocument();
  });

  test('renders deck when not shuffling', () => {
    renderWithTheme(
      <Deck
        cardCount={44}
        isShuffling={false}
      />
    );

    const deckContainer = screen.getByTestId('deck-container');
    expect(deckContainer).toBeInTheDocument();
  });

  test('does not show shuffle button', () => {
    renderWithTheme(
      <Deck
        cardCount={44}
        isShuffling={false}
      />
    );

    expect(screen.queryByText('Shuffle Deck')).not.toBeInTheDocument();
  });

  test('has correct styling and structure', () => {
    const { container } = renderWithTheme(
      <Deck
        cardCount={44}
        isShuffling={false}
      />
    );

    const deckElement = container.querySelector('.deck');
    expect(deckElement).toBeInTheDocument();

    const deckContainer = container.querySelector('.deck-container');
    expect(deckContainer).toBeInTheDocument();
  });

  test('renders correct number of visible cards', () => {
    const { container } = renderWithTheme(
      <Deck
        cardCount={44}
        isShuffling={false}
      />
    );

    const deckCards = container.querySelectorAll('.deck-card');
    expect(deckCards).toHaveLength(8); // Max visible cards
  });

  test('limits visible cards to 8 even with large deck', () => {
    const { container } = renderWithTheme(
      <Deck
        cardCount={100}
        isShuffling={false}
      />
    );

    const deckCards = container.querySelectorAll('.deck-card');
    expect(deckCards).toHaveLength(8);
  });
});
