import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { ThemeProvider } from '@mui/material/styles';
import { Card } from '../../components/Card';
import { fallingBirdTheme } from '../../theme/muiTheme';
import { DrawnCard } from '../../types/Card';

const mockCard: DrawnCard = {
  id: 'test-card-1',
  title: 'Test Card',
  suite: 'Test Suite',
  upright_meaning: 'This is a test card meaning',
  upright_key_interpretation: 'Test upright interpretation',
  upright_affirmation: 'Test affirmation',
  upright_journal_prompt: 'Test upright journal prompt',
  reversal_key_interpretation: 'Test reversal interpretation',
  reversal_journal_prompt: 'Test reversal journal prompt',
  reversal_meaning: 'Test reversal meaning',
  reversal_affirmation: 'Test reversal affirmation',
  isRevealed: false,
  isReversed: false,
  position: 0,
};

const mockPosition = {
  x: 100,
  y: 200,
  label: 'Test Position',
};

const renderWithTheme = (component: React.ReactElement) => {
  return render(
    <ThemeProvider theme={fallingBirdTheme}>
      {component}
    </ThemeProvider>
  );
};

describe('Card Component', () => {
  const mockOnReveal = jest.fn();

  beforeEach(() => {
    mockOnReveal.mockClear();
  });

  test('renders card back when not revealed', () => {
    renderWithTheme(
      <Card
        card={mockCard}
        onReveal={mockOnReveal}
        position={mockPosition}
        isDrawing={false}
      />
    );

    // Should show card back
    const cardBack = screen.getByAltText('Card back');
    expect(cardBack).toBeInTheDocument();

    // Should show click hint
    expect(screen.getByText('Click to reveal')).toBeInTheDocument();
  });

  test('renders card content when revealed', () => {
    const revealedCard = { ...mockCard, isRevealed: true };

    renderWithTheme(
      <Card
        card={revealedCard}
        onReveal={mockOnReveal}
        position={mockPosition}
        isDrawing={false}
      />
    );

    expect(screen.getByText('Test Card')).toBeInTheDocument();
    expect(screen.getByText('"Test affirmation"')).toBeInTheDocument();
    expect(screen.getByText('✦ Show Guidance ✦')).toBeInTheDocument();
  });

  test('calls onReveal when card is clicked and not revealed', () => {
    renderWithTheme(
      <Card
        card={mockCard}
        onReveal={mockOnReveal}
        position={mockPosition}
        isDrawing={false}
      />
    );

    const cardElement = screen.getByText('Click to reveal').closest('.card');
    fireEvent.click(cardElement!);

    expect(mockOnReveal).toHaveBeenCalledWith('test-card-1');
  });

  test('does not call onReveal when card is already revealed', () => {
    const revealedCard = { ...mockCard, isRevealed: true };

    renderWithTheme(
      <Card
        card={revealedCard}
        onReveal={mockOnReveal}
        position={mockPosition}
        isDrawing={false}
      />
    );

    const cardElement = screen.getByText('Test Card').closest('.card');
    fireEvent.click(cardElement!);

    expect(mockOnReveal).not.toHaveBeenCalled();
  });

  test('does not call onReveal when card is in drawing state', () => {
    renderWithTheme(
      <Card
        card={mockCard}
        onReveal={mockOnReveal}
        position={mockPosition}
        isDrawing={true}
      />
    );

    const cardElement = screen.getByText('Click to reveal').closest('.card');
    fireEvent.click(cardElement!);

    expect(mockOnReveal).not.toHaveBeenCalled();
  });

  test('displays position label when provided', () => {
    renderWithTheme(
      <Card
        card={mockCard}
        onReveal={mockOnReveal}
        position={mockPosition}
        isDrawing={false}
      />
    );

    expect(screen.getByText('Test Position')).toBeInTheDocument();
  });
});
